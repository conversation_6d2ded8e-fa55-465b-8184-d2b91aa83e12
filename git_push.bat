@echo off
echo 🚀 Git репозиторийга push қилиш...

echo 📦 Git репозиторийни инициализация қилиш...
git init

echo 👤 Git фойдаланувчини созлаш...
git config user.name "<PERSON><PERSON><PERSON><PERSON>"
git config user.email "<EMAIL>"

echo 🔗 Remote репозиторийни қўшиш...
git remote add origin https://github.com/ASayidov/RelefXisoblator.git

echo 📁 Файлларни staging area га қўшиш...
git add .

echo 💾 Биринчи commit...
git commit -m "feat: Add user API keys feature

- Хар бир фойдаланувчи ўзининг Google Elevation API калитини ишлатади
- Фойдаланувчи API калитларини бошқариш тизими
- Автоматик версия аниқлаш ва мослашув
- Батафсил русча қўлланма /help командасида
- Хавфсизлик ва изоляция API калитлар учун
- Автоматик запуск скрипти барча версиялар учун"

echo 🌿 Янги ветка яратиш...
git checkout -b user-api-keys-feature

echo 🚀 GitHub га push қилиш...
git push -u origin user-api-keys-feature

echo ✅ Тайёр! Репозиторий янгиланди.
pause
