import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

# Настройка логирования
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Путь к файлу конфигурации
CONFIG_FILE = "api_keys.json"

# Максимальное количество запросов в день для бесплатного использования Google Elevation API
# По умолчанию устанавливаем 2000 запросов в день (можно изменить)
DEFAULT_DAILY_LIMIT = 2000

# Список ID администраторов (Telegram user_id)
# Добавьте сюда ID пользователей, которым разрешено управлять API ключами
ADMIN_IDS = [
    1866714139
    # Пример: 123456789,
    # Добавьте сюда ID администраторов
]

class APIKeyManager:
    def __init__(self, config_file: str = CONFIG_FILE):
        self.config_file = config_file
        self.api_keys = {}
        self.current_key_index = 0
        self.load_config()

    def load_config(self) -> None:
        """Загружает конфигурацию API ключей из файла"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    self.api_keys = config.get('api_keys', {})
                    self.current_key_index = config.get('current_key_index', 0)
                logger.info(f"Загружена конфигурация API ключей. Доступно {len(self.api_keys)} ключей.")
            except Exception as e:
                logger.error(f"Ошибка при загрузке конфигурации API ключей: {e}")
                # Создаем пустую конфигурацию
                self.api_keys = {}
                self.current_key_index = 0
        else:
            # Если файл не существует, создаем пустую конфигурацию
            logger.info("Файл конфигурации не найден. Создаем новую конфигурацию.")
            self.api_keys = {}
            self.current_key_index = 0
            self.save_config()

    def save_config(self) -> None:
        """Сохраняет конфигурацию API ключей в файл"""
        try:
            config = {
                'api_keys': self.api_keys,
                'current_key_index': self.current_key_index
            }
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=4)
            logger.info("Конфигурация API ключей сохранена.")
        except Exception as e:
            logger.error(f"Ошибка при сохранении конфигурации API ключей: {e}")

    def add_api_key(self, key: str, daily_limit: int = DEFAULT_DAILY_LIMIT) -> bool:
        """Добавляет новый API ключ в конфигурацию"""
        if key in self.api_keys:
            logger.warning(f"API ключ {key} уже существует в конфигурации.")
            return False

        # Проверяем валидность ключа (можно добавить более сложную проверку)
        if not key or len(key) < 10:
            logger.error(f"API ключ {key} недействителен.")
            return False

        # Добавляем ключ в конфигурацию
        today = datetime.now().strftime('%Y-%m-%d')
        self.api_keys[key] = {
            'daily_limit': daily_limit,
            'usage': {
                today: 0
            },
            'added_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'is_active': True
        }
        self.save_config()
        logger.info(f"Добавлен новый API ключ: {key}")
        return True

    def remove_api_key(self, key: str) -> bool:
        """Удаляет API ключ из конфигурации"""
        if key not in self.api_keys:
            logger.warning(f"API ключ {key} не найден в конфигурации.")
            return False

        del self.api_keys[key]
        # Если удаляем текущий ключ, сбрасываем индекс
        if self.current_key_index >= len(self.get_active_keys()):
            self.current_key_index = 0

        self.save_config()
        logger.info(f"Удален API ключ: {key}")
        return True

    def get_active_keys(self) -> List[str]:
        """Возвращает список активных API ключей"""
        return [key for key, data in self.api_keys.items() if data.get('is_active', True)]

    def get_current_key(self) -> Optional[str]:
        """Возвращает текущий активный API ключ"""
        active_keys = self.get_active_keys()
        if not active_keys:
            logger.error("Нет доступных активных API ключей.")
            return None

        # Если текущий индекс выходит за пределы списка, сбрасываем его
        if self.current_key_index >= len(active_keys):
            self.current_key_index = 0
            self.save_config()

        return active_keys[self.current_key_index]

    def rotate_key(self) -> Optional[str]:
        """Переключается на следующий доступный API ключ"""
        active_keys = self.get_active_keys()
        if not active_keys:
            logger.error("Нет доступных активных API ключей для ротации.")
            return None

        self.current_key_index = (self.current_key_index + 1) % len(active_keys)
        self.save_config()
        logger.info(f"Переключение на API ключ: {active_keys[self.current_key_index]}")
        return active_keys[self.current_key_index]

    def increment_usage(self, key: Optional[str] = None) -> bool:
        """Увеличивает счетчик использования API ключа"""
        if key is None:
            key = self.get_current_key()
            if key is None:
                return False

        if key not in self.api_keys:
            logger.warning(f"API ключ {key} не найден в конфигурации.")
            return False

        today = datetime.now().strftime('%Y-%m-%d')
        if today not in self.api_keys[key]['usage']:
            self.api_keys[key]['usage'][today] = 0

        self.api_keys[key]['usage'][today] += 1
        self.save_config()

        # Проверяем, не превышен ли дневной лимит
        if self.api_keys[key]['usage'][today] >= self.api_keys[key]['daily_limit']:
            logger.warning(f"Достигнут дневной лимит для API ключа {key}.")
            # Если лимит превышен, деактивируем ключ на сегодня
            self.api_keys[key]['is_active'] = False
            self.save_config()
            # Пробуем переключиться на другой ключ
            self.rotate_key()

        return True

    def check_key_limit(self, key: Optional[str] = None) -> Tuple[bool, int, int]:
        """Проверяет, не превышен ли лимит для ключа

        Возвращает:
            (не превышен лимит, текущее использование, дневной лимит)
        """
        if key is None:
            key = self.get_current_key()
            if key is None:
                return False, 0, 0

        if key not in self.api_keys:
            logger.warning(f"API ключ {key} не найден в конфигурации.")
            return False, 0, 0

        today = datetime.now().strftime('%Y-%m-%d')
        if today not in self.api_keys[key]['usage']:
            self.api_keys[key]['usage'][today] = 0

        current_usage = self.api_keys[key]['usage'][today]
        daily_limit = self.api_keys[key]['daily_limit']

        return current_usage < daily_limit, current_usage, daily_limit

    def reset_daily_usage(self) -> None:
        """Сбрасывает счетчики использования для всех ключей на сегодня"""
        today = datetime.now().strftime('%Y-%m-%d')
        for key in self.api_keys:
            self.api_keys[key]['usage'][today] = 0
            self.api_keys[key]['is_active'] = True

        self.save_config()
        logger.info("Сброшены счетчики использования для всех API ключей.")

    def clean_old_usage_data(self, days: int = 30) -> None:
        """Удаляет старые данные об использовании API ключей"""
        cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

        for key in self.api_keys:
            usage = self.api_keys[key]['usage']
            # Удаляем записи старше cutoff_date
            self.api_keys[key]['usage'] = {
                date: count for date, count in usage.items()
                if date >= cutoff_date
            }

        self.save_config()
        logger.info(f"Удалены данные об использовании API ключей старше {days} дней.")

# Создаем глобальный экземпляр менеджера API ключей
api_key_manager = APIKeyManager()

# Функция для получения текущего API ключа
def get_api_key() -> Optional[str]:
    """Возвращает текущий активный API ключ и увеличивает счетчик использования"""
    key = api_key_manager.get_current_key()
    if key:
        api_key_manager.increment_usage(key)
    return key
