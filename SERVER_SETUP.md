# 🚀 Виндовс серверга ўрнатиш қўлланмаси

## 📦 Керакли файллар

Серверга кўчириш учун фақат қуйидаги файллар керак:

### ✅ Асосий файллар:
- `main.py` - асосий бот кодлари
- `api_config.py` - API калитларни бошқариш
- `requirements.txt` - Python пакетлари рўйхати
- `api_keys.json` - системали <PERSON> калитлар (бўш)
- `user_api_keys.json` - фойдаланувчи API калитлари (бўш)
- `README.md` - документация

### ❌ Керакмас файллар:
- `elevation_results.xlsx` - натижалар файли
- `temp.xlsx` - вақтинчалик файл
- `__pycache__/` - Python кеш
- `.vscode/`, `.idea/` - IDE созламалари

## 🔧 Серверда ўрнатиш қадамлари

### 1. Python ўрнатиш
```bash
# Python 3.8+ версиясини ўрнатинг
# https://www.python.org/downloads/ дан юклаб олинг
```

### 2. Файлларни кўчириш
```bash
# Барча керакли файлларни серверга кўчиринг
# Масалан: C:\relefbot\ папкасига
```

### 3. Пакетларни ўрнатиш
```bash
cd C:\relefbot
pip install -r requirements.txt
```

### 4. Ботни ишга тушириш
```bash
python main.py
```

## 🔑 Муҳим созламалар

### Bot Token
`main.py` файлида бот токенини ўзгартиринг:
```python
application = Application.builder().token("SIZNING_BOT_TOKENINGIZ").build()
```

### Admin ID
`api_config.py` файлида админ ID ни ўзгартиринг:
```python
ADMIN_IDS = [
    SIZNING_TELEGRAM_ID_INGIZ  # Рақам кўринишида
]
```

## 🔄 Автоматик ишга тушириш

### Windows Service сифатида ишлатиш учун:
1. `nssm` (Non-Sucking Service Manager) ўрнатинг
2. Қуйидаги командани ишлатинг:
```bash
nssm install RelefBot "C:\Python\python.exe" "C:\relefbot\main.py"
nssm start RelefBot
```

### Task Scheduler орқали:
1. Task Scheduler очинг
2. "Create Basic Task" танланг
3. "When the computer starts" танланг
4. Python скриптини кўрсатинг

## 📊 Мониторинг

### Логларни кўриш:
- Консолда логлар кўринади
- Хатоларни кузатиб туринг
- API калитлар ишлашини текширинг

### Файллар ҳолати:
- `api_keys.json` - системали калитлар
- `user_api_keys.json` - фойдаланувчи калитлари
- Бу файллар автоматик янгиланади

## 🔒 Хавфсизлик

### Файл рухсатлари:
- JSON файлларга фақат бот рухсати берилсин
- Бот токенини хавфсиз сақланг
- API калитларни бошқалар кўрмасин

### Backup:
- Муҳим файлларнинг захира нусхасини олинг
- Конфигурация файлларини сақланг

## 🆘 Муаммоларни ҳал қилиш

### Бот ишламаса:
1. Python версиясини текширинг (3.8+)
2. Пакетлар ўрнатилганини текширинг
3. Бот токенини текширинг
4. Интернет алоқасини текширинг

### API хатолари:
1. Фойдаланувчилар API калит қўйганини текширинг
2. Google Cloud биллинг ёқилганини текширинг
3. API лимитларини кузатинг

## 📞 Ёрдам

Муаммо бўлса:
1. Логларни кўринг
2. Хатолик хабарларини ўқинг
3. Конфигурацияни текширинг
4. Документацияни кўринг
