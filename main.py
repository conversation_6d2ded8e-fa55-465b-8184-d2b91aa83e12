import nest_asyncio
import asyncio
import requests
import pandas as pd
import logging
import json
from datetime import datetime
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, CallbackContext
import os
import pytz
os.environ['TZ'] = 'UTC'
from time import sleep
from api_config import api_key_manager, get_api_key, user_api_key_manager, get_user_api_key, ADMIN_IDS

# Патчим текущий цикл событий
nest_asyncio.apply()

# Настройка логирования
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Функция для получения высоты по координатам с использованием Google Maps Elevation API
def get_elevation(lat, lon, user_id=None, retries=3, delay=2):
    # Сначала пробуем получить API ключ пользователя
    api_key = None
    if user_id:
        api_key = get_user_api_key(user_id)

    # Если у пользователя нет API ключа, используем системный (для совместимости)
    if not api_key:
        api_key = get_api_key()
        if not api_key:
            logger.error("Нет доступных API ключей для использования.")
            return None

    api_url = f"https://maps.googleapis.com/maps/api/elevation/json?locations={lat},{lon}&key={api_key}"
    for attempt in range(retries):
        try:
            response = requests.get(api_url)
            response.raise_for_status()  # Проверка на успешный ответ
            logger.info(f"API response: {response.text}")  # Логирование ответа API
            data = response.json()

            # Проверяем, есть ли ошибка в ответе API
            if 'error_message' in data:
                error_message = data['error_message']
                status = data.get('status', 'UNKNOWN_ERROR')
                logger.error(f"Ошибка API: {status} - {error_message}")

                # Если ошибка связана с превышением лимита или недействительным ключом,
                # пробуем переключиться на другой ключ
                if status in ['OVER_QUERY_LIMIT', 'REQUEST_DENIED', 'INVALID_REQUEST']:
                    logger.info(f"Переключаемся на другой API ключ из-за ошибки: {status}")
                    api_key = api_key_manager.rotate_key()
                    if api_key:
                        api_url = f"https://maps.googleapis.com/maps/api/elevation/json?locations={lat},{lon}&key={api_key}"
                        continue
                    else:
                        logger.error("Нет доступных API ключей для переключения.")
                        return None

                return None

            return data['results'][0]['elevation']
        except requests.exceptions.RequestException as e:
            logger.error(f"Ошибка при запросе к API: {e}")
            if attempt < retries - 1:
                sleep(delay)  # Задержка перед повторной попыткой
            else:
                # Если все попытки не удались, пробуем переключиться на другой ключ
                api_key = api_key_manager.rotate_key()
                if api_key:
                    logger.info(f"Переключаемся на другой API ключ после неудачных попыток")
                    api_url = f"https://maps.googleapis.com/maps/api/elevation/json?locations={lat},{lon}&key={api_key}"
                else:
                    logger.error("Нет доступных API ключей для переключения.")
                    return None
        except ValueError as e:
            logger.error(f"Ошибка при обработке JSON: {e}")
            return None

# Функция для обработки команды /start
async def start(update: Update, context: CallbackContext) -> None:
    user_id = update.effective_user.id

    # Проверяем, есть ли у пользователя API ключ
    if user_api_key_manager.has_user_api_key(user_id):
        await update.message.reply_text(
            '🎉 Добро пожаловать! У вас уже настроен API ключ.\n\n'
            '📁 Отправьте мне Excel файл с данными в формате:\n'
            'широта долгота; азимут; дистанция\n\n'
            '📝 Например:\n'
            '40.53648 70.94076;120;1000\n'
            '40.53648 70.94076;120;1000\n'
            '40.53648 70.94076;120;1000\n\n'
            '⚙️ Команды:\n'
            '/mykey - посмотреть информацию о вашем API ключе\n'
            '/setkey - изменить API ключ\n'
            '/help - получить подробную справку'
        )
    else:
        await update.message.reply_text(
            '👋 Добро пожаловать в бот для расчета рельефа!\n\n'
            '🔑 Для работы вам нужно установить свой Google Elevation API ключ.\n\n'
            '📋 Как получить API ключ:\n'
            '1. Перейдите на https://console.cloud.google.com/\n'
            '2. Создайте новый проект или выберите существующий\n'
            '3. Включите Google Maps Elevation API\n'
            '4. Создайте API ключ в разделе "Credentials"\n'
            '5. Используйте команду /setkey для установки ключа\n\n'
            '⚙️ Команды:\n'
            '/setkey <ваш_api_ключ> - установить API ключ\n'
            '/help - получить подробную справку\n\n'
            '💡 Пример: /setkey AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
        )

# Функция для обработки команды /help
async def help_command(update: Update, context: CallbackContext) -> None:
    user_id = update.effective_user.id

    base_help = (
        '📖 Справка по использованию бота\n\n'
        '📁 Для работы отправьте Excel файл с данными в формате:\n'
        'coords; azimuth; distance\n\n'
        '📝 Где:\n'
        '• coords: широта и долгота через пробел (например, "40.53648 70.94076")\n'
        '• azimuth: азимут в градусах (например, "120")\n'
        '• distance: дистанция в метрах (например, "1000")\n\n'
        '📋 Пример содержимого Excel файла:\n'
        '40.53648 70.94076;120;1000\n'
        '40.53648 70.94076;120;1000\n'
        '40.53648 70.94076;120;1000\n\n'
        '📊 После отправки файла бот вычислит разницу высот и отправит результат.\n\n'
    )

    if user_api_key_manager.has_user_api_key(user_id):
        user_commands = (
            '⚙️ Ваши команды:\n'
            '/mykey - информация о вашем API ключе\n'
            '/setkey <новый_ключ> - изменить API ключ\n'
            '/removekey - удалить ваш API ключ\n'
        )
    else:
        user_commands = (
            '🔑 Команды для настройки:\n'
            '/setkey <ваш_api_ключ> - установить API ключ\n\n'
            '📋 Как получить API ключ:\n'
            '1. Перейдите на https://console.cloud.google.com/\n'
            '2. Создайте проект или выберите существующий\n'
            '3. Включите Google Maps Elevation API\n'
            '4. Создайте API ключ в разделе "Credentials"\n'
            '5. Пример: /setkey AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx\n'
        )

    # Добавляем админские команды для администраторов
    admin_commands = ''
    if update.effective_user.id in ADMIN_IDS:
        admin_commands = (
            '\n👑 Команды администратора:\n'
            '/addkey <ключ> <лимит> - добавить системный API ключ\n'
            '/listkeys - показать системные API ключи\n'
            '/removesystemkey <ключ> - удалить системный API ключ\n'
            '/resetusage - сбросить счетчики системных ключей\n'
        )

    await update.message.reply_text(base_help + user_commands + admin_commands)

# Функция для установки пользовательского API ключа
async def set_user_api_key(update: Update, context: CallbackContext) -> None:
    user_id = update.effective_user.id

    # Проверяем аргументы команды
    if not context.args or len(context.args) < 1:
        await update.message.reply_text(
            '❌ Пожалуйста, укажите API ключ.\n\n'
            '📝 Пример: /setkey AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx\n\n'
            '📋 Как получить API ключ:\n'
            '1. Перейдите на https://console.cloud.google.com/\n'
            '2. Создайте проект или выберите существующий\n'
            '3. Включите Google Maps Elevation API\n'
            '4. Создайте API ключ в разделе "Credentials"'
        )
        return

    api_key = context.args[0]

    # Устанавливаем ключ
    if user_api_key_manager.set_user_api_key(user_id, api_key):
        await update.message.reply_text(
            '✅ API ключ успешно установлен!\n\n'
            '📁 Теперь вы можете отправлять Excel файлы для обработки.\n'
            '📊 Используйте /mykey для просмотра информации о ключе.'
        )
    else:
        await update.message.reply_text(
            '❌ Не удалось установить API ключ.\n'
            'Убедитесь, что ключ действителен и имеет правильный формат.'
        )

# Функция для просмотра информации о пользовательском API ключе
async def show_user_api_key(update: Update, context: CallbackContext) -> None:
    user_id = update.effective_user.id

    if not user_api_key_manager.has_user_api_key(user_id):
        await update.message.reply_text(
            '❌ У вас не установлен API ключ.\n\n'
            '🔑 Используйте команду /setkey <ваш_api_ключ> для установки ключа.'
        )
        return

    # Получаем информацию о ключе
    api_key = user_api_key_manager.get_user_api_key(user_id)
    usage_today = user_api_key_manager.get_user_usage(user_id)

    # Маскируем ключ для безопасности
    masked_key = api_key[:4] + "..." + api_key[-4:] if len(api_key) > 8 else "***"

    await update.message.reply_text(
        f'🔑 Информация о вашем API ключе:\n\n'
        f'🔐 Ключ: {masked_key}\n'
        f'📊 Использовано сегодня: {usage_today} запросов\n\n'
        f'⚙️ Команды:\n'
        f'/setkey <новый_ключ> - изменить ключ\n'
        f'/removekey - удалить ключ'
    )

# Функция для удаления пользовательского API ключа
async def remove_user_api_key(update: Update, context: CallbackContext) -> None:
    user_id = update.effective_user.id

    if not user_api_key_manager.has_user_api_key(user_id):
        await update.message.reply_text(
            '❌ У вас не установлен API ключ.'
        )
        return

    # Удаляем ключ
    if user_api_key_manager.remove_user_api_key(user_id):
        await update.message.reply_text(
            '✅ Ваш API ключ успешно удален.\n\n'
            '🔑 Используйте /setkey <ваш_api_ключ> для установки нового ключа.'
        )
    else:
        await update.message.reply_text(
            '❌ Не удалось удалить API ключ.'
        )

# Функция для добавления нового API ключа (админская)
async def add_api_key(update: Update, context: CallbackContext) -> None:
    # Проверяем, есть ли у пользователя права администратора
    if not await is_admin(update):
        await update.message.reply_text('У вас нет прав для выполнения этой команды.')
        return

    # Проверяем аргументы команды
    if not context.args or len(context.args) < 1:
        await update.message.reply_text('Пожалуйста, укажите API ключ. Пример: /addkey YOUR_API_KEY 2000')
        return

    api_key = context.args[0]

    # Если указан лимит, используем его, иначе используем значение по умолчанию
    daily_limit = 2000  # Значение по умолчанию
    if len(context.args) > 1:
        try:
            daily_limit = int(context.args[1])
        except ValueError:
            await update.message.reply_text('Неверный формат лимита. Пожалуйста, укажите число.')
            return

    # Добавляем ключ
    if api_key_manager.add_api_key(api_key, daily_limit):
        await update.message.reply_text(f'API ключ успешно добавлен с дневным лимитом {daily_limit}.')
    else:
        await update.message.reply_text('Не удалось добавить API ключ. Возможно, он уже существует или недействителен.')

# Функция для вывода списка API ключей
async def list_api_keys(update: Update, context: CallbackContext) -> None:
    # Проверяем, есть ли у пользователя права администратора
    if not await is_admin(update):
        await update.message.reply_text('У вас нет прав для выполнения этой команды.')
        return

    active_keys = api_key_manager.get_active_keys()
    if not api_key_manager.api_keys:
        await update.message.reply_text('Нет доступных API ключей.')
        return

    current_key = api_key_manager.get_current_key()
    message = "Список API ключей:\n\n"

    for key, data in api_key_manager.api_keys.items():
        # Маскируем часть ключа для безопасности
        masked_key = key[:4] + "..." + key[-4:]

        # Получаем сегодняшнее использование
        today = datetime.now().strftime('%Y-%m-%d')
        today_usage = data['usage'].get(today, 0)

        # Статус ключа
        status = "Активен" if data.get('is_active', True) else "Неактивен"
        if key == current_key:
            status += " (текущий)"

        message += f"Ключ: {masked_key}\n"
        message += f"Статус: {status}\n"
        message += f"Лимит: {data['daily_limit']} запросов в день\n"
        message += f"Использовано сегодня: {today_usage} запросов\n"
        message += f"Добавлен: {data['added_date']}\n\n"

    await update.message.reply_text(message)

# Функция для удаления системного API ключа (админская)
async def remove_system_api_key(update: Update, context: CallbackContext) -> None:
    # Проверяем, есть ли у пользователя права администратора
    if not await is_admin(update):
        await update.message.reply_text('У вас нет прав для выполнения этой команды.')
        return

    # Проверяем аргументы команды
    if not context.args or len(context.args) < 1:
        await update.message.reply_text('Пожалуйста, укажите системный API ключ для удаления.')
        return

    api_key = context.args[0]

    # Удаляем ключ
    if api_key_manager.remove_api_key(api_key):
        await update.message.reply_text('Системный API ключ успешно удален.')
    else:
        await update.message.reply_text('Не удалось удалить системный API ключ. Возможно, он не существует.')

# Функция для сброса счетчиков использования
async def reset_usage(update: Update, context: CallbackContext) -> None:
    # Проверяем, есть ли у пользователя права администратора
    if not await is_admin(update):
        await update.message.reply_text('У вас нет прав для выполнения этой команды.')
        return

    api_key_manager.reset_daily_usage()
    await update.message.reply_text('Счетчики использования для всех API ключей сброшены.')

# Функция для проверки прав администратора
async def is_admin(update: Update) -> bool:
    # Получаем ID пользователя
    user_id = update.effective_user.id

    # Проверяем, есть ли ID пользователя в списке администраторов
    is_admin_user = user_id in ADMIN_IDS

    # Если список администраторов пуст, разрешаем доступ первому пользователю
    # и добавляем его в список администраторов
    if not ADMIN_IDS and user_id:
        ADMIN_IDS.append(user_id)
        logger.info(f"Добавлен первый администратор с ID: {user_id}")
        return True

    # Логируем попытку доступа к админ-командам
    if not is_admin_user:
        logger.warning(f"Пользователь с ID {user_id} пытался получить доступ к админ-командам")

    return is_admin_user

# Функция для обработки документов
async def handle_document(update: Update, context: CallbackContext) -> None:
    user_id = update.effective_user.id

    # Проверяем, есть ли у пользователя API ключ
    if not user_api_key_manager.has_user_api_key(user_id):
        await update.message.reply_text(
            '❌ У вас не установлен API ключ!\n\n'
            '🔑 Для работы с ботом необходимо установить свой Google Elevation API ключ.\n\n'
            '📋 Как получить API ключ:\n'
            '1. Перейдите на https://console.cloud.google.com/\n'
            '2. Создайте проект или выберите существующий\n'
            '3. Включите Google Maps Elevation API\n'
            '4. Создайте API ключ в разделе "Credentials"\n'
            '5. Используйте команду /setkey <ваш_api_ключ> для установки ключа\n\n'
            '💡 Пример: /setkey AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
        )
        return

    try:
        # Получаем файл из сообщения
        file = await context.bot.get_file(update.message.document.file_id)
        file_path = file.file_path

        # Читаем данные из Excel файла
        df = pd.read_excel(file_path)

        results = []
        for index, row in df.iterrows():
            # Получаем координаты, азимут и дистанцию из каждой строки
            parts = row['coords'].strip().split(';')
            if len(parts) != 3:
                raise ValueError("Неверный формат ввода. Ожидается три части, разделенные точкой с запятой.")

            coords = parts[0].strip().split()
            if len(coords) != 2:
                raise ValueError("Неверный формат координат. Ожидается широта и долгота, разделенные пробелом.")

            lat, lon = map(float, coords)
            azimuth = float(parts[1].strip())
            distance = float(parts[2].strip())

            # Проверяем диапазоны значений
            if not (-90 <= lat <= 90):    raise ValueError("Широта должна быть в диапазоне от -90 до 90.")
            if not (-180 <= lon <= 180):
                raise ValueError("Долгота должна быть в диапазоне от -180 до 180.")
            if not (0 <= azimuth <= 360):
                raise ValueError("Азимут должен быть в диапазоне от 0 до 360.")
            if distance < 0:
                raise ValueError("Дистанция должна быть положительным числом.")

            # Вычисляем конечные координаты (упрощенно, без учета кривизны Земли)
            import math
            R = 6371e3  # Радиус Земли в метрах
            delta_lat = distance * math.cos(math.radians(azimuth)) / R
            delta_lon = distance * math.sin(math.radians(azimuth)) / (R * math.cos(math.radians(lat)))

            end_lat = lat + math.degrees(delta_lat)
            end_lon = lon + math.degrees(delta_lon)

            # Получаем высоты начальной и конечной точек
            start_elevation = get_elevation(lat, lon, user_id)
            end_elevation = get_elevation(end_lat, end_lon, user_id)

            if start_elevation is None or end_elevation is None:
                raise ValueError("Не удалось получить данные о высоте. Пожалуйста, попробуйте позже.")

            # Вычисляем разницу высот
            elevation_difference = end_elevation - start_elevation

            # Определяем знак разницы высот
            if elevation_difference > 0:
                elevation_difference = -abs(elevation_difference)
            else:
                elevation_difference = abs(elevation_difference)

            results.append(elevation_difference)

        # Добавляем результаты в исходный DataFrame рядом с исходными данными
        df['Elevation Difference'] = results

        # Записываем результаты в новый Excel файл
        df.to_excel('elevation_results.xlsx', index=False)

        # Отправляем ответ пользователю с результатами в виде файла Excel
        await context.bot.send_document(chat_id=update.message.chat_id, document=open('elevation_results.xlsx', 'rb'))
    except ValueError as ve:
        logger.error(f'Ошибка ввода: {ve}')
        await update.message.reply_text(f'Ошибка ввода: {ve}\n'
                                        'Пожалуйста, используйте формат:\n'
                                        'широта долгота; азимут; дистанция\n'
                                        'Например:\n'
                                        '40.53648 70.94076;120;1000\n'
                                        '40.53648 70.94076;120;1000\n'
                                        '40.53648 70.94076;120;1000')
    except Exception as e:
        logger.error(f'Произошла ошибка: {e}')
        await update.message.reply_text(f'Произошла ошибка: {e}')

# Эта функция не нужна для старой версии python-telegram-bot

async def main():
    # Вставьте сюда ваш токен
    #application = Application.builder().token("7691215008:AAGgkRjMnkTwevgdaPKXWXrSVFHB_vNB844").job_queue(None).build()
    application = Application.builder().token("7960086091:AAFuUhT2Tp-t1pinK7J1TrWBigegIk-cGQI").job_queue(None).build()

    # Добавляем обработчики команд
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))

    # Добавляем обработчики для пользовательских API ключей
    application.add_handler(CommandHandler("setkey", set_user_api_key))
    application.add_handler(CommandHandler("mykey", show_user_api_key))
    application.add_handler(CommandHandler("removekey", remove_user_api_key))

    # Добавляем обработчики для управления системными API ключами (админские)
    application.add_handler(CommandHandler("addkey", add_api_key))
    application.add_handler(CommandHandler("listkeys", list_api_keys))
    application.add_handler(CommandHandler("removesystemkey", remove_system_api_key))
    application.add_handler(CommandHandler("resetusage", reset_usage))

    # Обработчик для документов (Excel файлов)
    application.add_handler(MessageHandler(filters.Document.ALL, handle_document))

    # Инициализируем API ключи из конфигурации
    # Если в конфигурации нет ключей, добавляем стандартный ключ (замените на свой)
    if not api_key_manager.get_active_keys():
        default_key = "YOUR_BACKUP_API_KEY_HERE"  # Замените на свой резервный ключ
        if default_key != "YOUR_BACKUP_API_KEY_HERE":
            api_key_manager.add_api_key(default_key, 2000)  # Устанавливаем лимит 2000 запросов в день
            logger.info(f"Добавлен стандартный API ключ")
        else:
            logger.warning("Стандартный API ключ не настроен. Пользователи должны установить свои ключи.")

    # Запускаем бота
    await application.run_polling()

if __name__ == '__main__':
    import asyncio
    asyncio.run(main())