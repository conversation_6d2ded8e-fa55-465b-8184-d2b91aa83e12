# Эски версия python-telegram-bot (13.x) учун
import logging
import pandas as pd
import requests
import time
import math
import asyncio
import nest_asyncio
from telegram import Update
from telegram.ext import Up<PERSON><PERSON>, Command<PERSON><PERSON>ler, MessageHandler, Filters, CallbackContext
import os
import pytz
os.environ['TZ'] = 'UTC'

# nest_asyncio ни фаоллаштириш
nest_asyncio.apply()

from api_config import api_key_manager, get_api_key, user_api_key_manager, get_user_api_key, ADMIN_IDS

# Логлашни созлаш
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Функция для получения высоты по координатам с использованием Google Maps Elevation API
def get_elevation(lat, lon, user_id=None, retries=3, delay=2):
    # Сначала пробуем получить API ключ пользователя
    api_key = None
    if user_id:
        api_key = get_user_api_key(user_id)
    
    # Если у пользователя нет API ключа, используем системный (для совместимости)
    if not api_key:
        api_key = get_api_key()
        if not api_key:
            logger.error("Нет доступных API ключей для использования.")
            return None

    url = f"https://maps.googleapis.com/maps/api/elevation/json?locations={lat},{lon}&key={api_key}"
    
    for attempt in range(retries):
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data['status'] == 'OK' and data['results']:
                elevation = data['results'][0]['elevation']
                logger.info(f"Получена высота для координат ({lat}, {lon}): {elevation} м")
                return elevation
            else:
                logger.error(f"Ошибка API: {data.get('status', 'Unknown error')}")
                if data.get('status') == 'OVER_QUERY_LIMIT':
                    logger.warning("Превышен лимит запросов для текущего API ключа")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Ошибка запроса (попытка {attempt + 1}/{retries}): {e}")
            if attempt < retries - 1:
                time.sleep(delay)
            else:
                return None
        except Exception as e:
            logger.error(f"Неожиданная ошибка: {e}")
            return None

# Функция для вычисления новых координат по азимуту и дистанции
def calculate_new_coordinates(lat, lon, azimuth, distance):
    # Радиус Земли в метрах
    R = 6378137
    
    # Преобразуем в радианы
    lat_rad = math.radians(lat)
    lon_rad = math.radians(lon)
    azimuth_rad = math.radians(azimuth)
    
    # Вычисляем новые координаты
    new_lat_rad = math.asin(math.sin(lat_rad) * math.cos(distance / R) +
                           math.cos(lat_rad) * math.sin(distance / R) * math.cos(azimuth_rad))
    
    new_lon_rad = lon_rad + math.atan2(math.sin(azimuth_rad) * math.sin(distance / R) * math.cos(lat_rad),
                                      math.cos(distance / R) - math.sin(lat_rad) * math.sin(new_lat_rad))
    
    # Преобразуем обратно в градусы
    new_lat = math.degrees(new_lat_rad)
    new_lon = math.degrees(new_lon_rad)
    
    return new_lat, new_lon

# Функция для проверки прав администратора
def is_admin(update: Update) -> bool:
    return update.effective_user.id in ADMIN_IDS

# Функция для обработки команды /start
def start(update: Update, context: CallbackContext) -> None:
    user_id = update.effective_user.id
    
    # Проверяем, есть ли у пользователя API ключ
    if user_api_key_manager.has_user_api_key(user_id):
        update.message.reply_text(
            '🎉 Добро пожаловать! У вас уже настроен API ключ.\n\n'
            '📁 Отправьте мне Excel файл с данными в формате:\n'
            'широта долгота; азимут; дистанция\n\n'
            '📝 Например:\n'
            '40.53648 70.94076;120;1000\n'
            '40.53648 70.94076;120;1000\n'
            '40.53648 70.94076;120;1000\n\n'
            '⚙️ Команды:\n'
            '/mykey - посмотреть информацию о вашем API ключе\n'
            '/setkey - изменить API ключ\n'
            '/help - получить подробную справку'
        )
    else:
        update.message.reply_text(
            '👋 Добро пожаловать в бот для расчета рельефа!\n\n'
            '🔑 Для работы вам нужно установить свой Google Elevation API ключ.\n\n'
            '📋 Как получить API ключ:\n'
            '1. Перейдите на https://console.cloud.google.com/\n'
            '2. Создайте новый проект или выберите существующий\n'
            '3. Включите Google Maps Elevation API\n'
            '4. Создайте API ключ в разделе "Credentials"\n'
            '5. Используйте команду /setkey для установки ключа\n\n'
            '⚙️ Команды:\n'
            '/setkey <ваш_api_ключ> - установить API ключ\n'
            '/help - получить подробную справку\n\n'
            '💡 Пример: /setkey AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
        )

# Функция для установки пользовательского API ключа
def set_user_api_key(update: Update, context: CallbackContext) -> None:
    user_id = update.effective_user.id
    
    # Проверяем аргументы команды
    if not context.args or len(context.args) < 1:
        update.message.reply_text(
            '❌ Пожалуйста, укажите API ключ.\n\n'
            '📝 Пример: /setkey AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx\n\n'
            '📋 Как получить API ключ:\n'
            '1. Перейдите на https://console.cloud.google.com/\n'
            '2. Создайте проект или выберите существующий\n'
            '3. Включите Google Maps Elevation API\n'
            '4. Создайте API ключ в разделе "Credentials"'
        )
        return

    api_key = context.args[0]

    # Устанавливаем ключ
    if user_api_key_manager.set_user_api_key(user_id, api_key):
        update.message.reply_text(
            '✅ API ключ успешно установлен!\n\n'
            '📁 Теперь вы можете отправлять Excel файлы для обработки.\n'
            '📊 Используйте /mykey для просмотра информации о ключе.'
        )
    else:
        update.message.reply_text(
            '❌ Не удалось установить API ключ.\n'
            'Убедитесь, что ключ действителен и имеет правильный формат.'
        )

# Функция для просмотра информации о пользовательском API ключе
def show_user_api_key(update: Update, context: CallbackContext) -> None:
    user_id = update.effective_user.id
    
    if not user_api_key_manager.has_user_api_key(user_id):
        update.message.reply_text(
            '❌ У вас не установлен API ключ.\n\n'
            '🔑 Используйте команду /setkey <ваш_api_ключ> для установки ключа.'
        )
        return

    # Получаем информацию о ключе
    api_key = user_api_key_manager.get_user_api_key(user_id)
    usage_today = user_api_key_manager.get_user_usage(user_id)
    
    # Маскируем ключ для безопасности
    masked_key = api_key[:4] + "..." + api_key[-4:] if len(api_key) > 8 else "***"
    
    update.message.reply_text(
        f'🔑 Информация о вашем API ключе:\n\n'
        f'🔐 Ключ: {masked_key}\n'
        f'📊 Использовано сегодня: {usage_today} запросов\n\n'
        f'⚙️ Команды:\n'
        f'/setkey <новый_ключ> - изменить ключ\n'
        f'/removekey - удалить ключ'
    )

# Функция для удаления пользовательского API ключа
def remove_user_api_key(update: Update, context: CallbackContext) -> None:
    user_id = update.effective_user.id
    
    if not user_api_key_manager.has_user_api_key(user_id):
        update.message.reply_text(
            '❌ У вас не установлен API ключ.'
        )
        return

    # Удаляем ключ
    if user_api_key_manager.remove_user_api_key(user_id):
        update.message.reply_text(
            '✅ Ваш API ключ успешно удален.\n\n'
            '🔑 Используйте /setkey <ваш_api_ключ> для установки нового ключа.'
        )
    else:
        update.message.reply_text(
            '❌ Не удалось удалить API ключ.'
        )

# Функция для обработки команды /help
def help_command(update: Update, context: CallbackContext) -> None:
    user_id = update.effective_user.id
    
    base_help = (
        '📖 Справка по использованию бота\n\n'
        '📁 Для работы отправьте Excel файл с данными в формате:\n'
        'coords; azimuth; distance\n\n'
        '📝 Где:\n'
        '• coords: широта и долгота через пробел (например, "40.53648 70.94076")\n'
        '• azimuth: азимут в градусах (например, "120")\n'
        '• distance: дистанция в метрах (например, "1000")\n\n'
        '📋 Пример содержимого Excel файла:\n'
        '40.53648 70.94076;120;1000\n'
        '40.53648 70.94076;120;1000\n'
        '40.53648 70.94076;120;1000\n\n'
        '📊 После отправки файла бот вычислит разницу высот и отправит результат.\n\n'
    )
    
    # Отправляем базовую справку
    update.message.reply_text(base_help)
    
    if user_api_key_manager.has_user_api_key(user_id):
        user_commands = (
            '⚙️ Ваши команды:\n'
            '/mykey - информация о вашем API ключе\n'
            '/setkey <новый_ключ> - изменить API ключ\n'
            '/removekey - удалить ваш API ключ\n'
        )
        update.message.reply_text(user_commands)
    else:
        setup_help = (
            '🔑 Для настройки API ключа используйте:\n'
            '/setkey <ваш_api_ключ>\n\n'
            '💡 Пример: /setkey AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
        )
        update.message.reply_text(setup_help)

# Функция для обработки документов
def handle_document(update: Update, context: CallbackContext) -> None:
    user_id = update.effective_user.id
    
    # Проверяем, есть ли у пользователя API ключ
    if not user_api_key_manager.has_user_api_key(user_id):
        update.message.reply_text(
            '❌ У вас не установлен API ключ!\n\n'
            '🔑 Для работы с ботом необходимо установить свой Google Elevation API ключ.\n\n'
            '💡 Используйте команду: /setkey <ваш_api_ключ>'
        )
        return
    
    try:
        # Получаем файл из сообщения
        file = context.bot.get_file(update.message.document.file_id)
        file_path = file.file_path

        # Читаем данные из Excel файла
        df = pd.read_excel(file_path)
        
        # Проверяем наличие необходимых столбцов
        if df.shape[1] < 3:
            update.message.reply_text('❌ Файл должен содержать минимум 3 столбца: координаты, азимут, дистанция')
            return

        results = []
        
        for index, row in df.iterrows():
            try:
                # Парсим данные из первого столбца (координаты)
                coords_str = str(row.iloc[0])
                coords_parts = coords_str.split()
                
                if len(coords_parts) != 2:
                    results.append({
                        'Строка': index + 1,
                        'Начальная широта': 'Ошибка',
                        'Начальная долгота': 'Ошибка',
                        'Конечная широта': 'Ошибка',
                        'Конечная долгота': 'Ошибка',
                        'Начальная высота (м)': 'Ошибка парсинга координат',
                        'Конечная высота (м)': 'Ошибка парсинга координат',
                        'Разница высот (м)': 'Ошибка парсинга координат'
                    })
                    continue
                
                lat = float(coords_parts[0])
                lon = float(coords_parts[1])
                azimuth = float(row.iloc[1])
                distance = float(row.iloc[2])
                
                # Вычисляем конечные координаты
                end_lat, end_lon = calculate_new_coordinates(lat, lon, azimuth, distance)
                
                # Получаем высоты начальной и конечной точек
                start_elevation = get_elevation(lat, lon, user_id)
                end_elevation = get_elevation(end_lat, end_lon, user_id)
                
                if start_elevation is not None and end_elevation is not None:
                    elevation_diff = end_elevation - start_elevation
                    
                    results.append({
                        'Строка': index + 1,
                        'Начальная широта': lat,
                        'Начальная долгота': lon,
                        'Конечная широта': round(end_lat, 6),
                        'Конечная долгота': round(end_lon, 6),
                        'Начальная высота (м)': round(start_elevation, 2),
                        'Конечная высота (м)': round(end_elevation, 2),
                        'Разница высот (м)': round(elevation_diff, 2)
                    })
                else:
                    results.append({
                        'Строка': index + 1,
                        'Начальная широта': lat,
                        'Начальная долгота': lon,
                        'Конечная широта': round(end_lat, 6),
                        'Конечная долгота': round(end_lon, 6),
                        'Начальная высота (м)': 'Ошибка API',
                        'Конечная высота (м)': 'Ошибка API',
                        'Разница высот (м)': 'Ошибка API'
                    })
                
                # Небольшая задержка между запросами
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Ошибка обработки строки {index + 1}: {e}")
                results.append({
                    'Строка': index + 1,
                    'Начальная широта': 'Ошибка',
                    'Начальная долгота': 'Ошибка',
                    'Конечная широта': 'Ошибка',
                    'Конечная долгота': 'Ошибка',
                    'Начальная высота (м)': f'Ошибка: {str(e)}',
                    'Конечная высота (м)': f'Ошибка: {str(e)}',
                    'Разница высот (м)': f'Ошибка: {str(e)}'
                })

        # Создаем DataFrame с результатами
        results_df = pd.DataFrame(results)
        
        # Сохраняем в Excel файл
        output_filename = 'elevation_results.xlsx'
        results_df.to_excel(output_filename, index=False)
        
        # Отправляем файл пользователю
        with open(output_filename, 'rb') as file:
            context.bot.send_document(
                chat_id=update.effective_chat.id,
                document=file,
                filename=output_filename,
                caption=f'✅ Обработка завершена!\n📊 Обработано строк: {len(results)}'
            )
        
        # Удаляем временный файл
        import os
        if os.path.exists(output_filename):
            os.remove(output_filename)
            
    except Exception as e:
        logger.error(f"Ошибка при обработке документа: {e}")
        update.message.reply_text(f'❌ Произошла ошибка при обработке файла: {str(e)}')

def main():
    # Вставьте сюда ваш токен
    updater = Updater("7691215008:AAGgkRjMnkTwevgdaPKXWXrSVFHB_vNB844", use_context=True)
    
    # Получаем диспетчер для регистрации обработчиков
    dispatcher = updater.dispatcher

    # Добавляем обработчики команд
    dispatcher.add_handler(CommandHandler("start", start))
    dispatcher.add_handler(CommandHandler("help", help_command))

    # Добавляем обработчики для пользовательских API ключей
    dispatcher.add_handler(CommandHandler("setkey", set_user_api_key))
    dispatcher.add_handler(CommandHandler("mykey", show_user_api_key))
    dispatcher.add_handler(CommandHandler("removekey", remove_user_api_key))

    # Обработчик для документов (Excel файлов)
    dispatcher.add_handler(MessageHandler(Filters.document, handle_document))

    # Инициализируем API ключи из конфигурации
    if not api_key_manager.get_active_keys():
        logger.warning("Системные API ключи не настроены. Пользователи должны установить свои ключи.")

    # Запускаем бота
    updater.start_polling()
    updater.idle()

if __name__ == '__main__':
    main()
