🤖 АВТОМАТИЧЕСКИЙ ЗАПУСК TELEGRAM БОТА

📦 В архиве:
- run_bot.py - автоматический запуск
- api_config.py - управление API ключами  
- api_keys.json - системные ключи (пустой)
- user_api_keys.json - пользовательские ключи (пустой)

🚀 ЗАПУСК:
1. Распакуйте архив в папку (например: C:\relefbot\)
2. Откройте командную строку в этой папке
3. Выполните команду: python run_bot.py

✅ ВСЁ! Бот автоматически:
- Определит версию python-telegram-bot
- Установит недостающие пакеты
- Запустится с правильными настройками

🔧 НАСТРОЙКИ:
- Бот токен уже прописан в коде
- Админ ID: 1866714139 (ваш)
- Пользователи сами добавляют API ключи через /setkey

📱 КОМАНДЫ БОТА:
/start - начать работу
/setkey <ключ> - установить API ключ
/mykey - информация о ключе
/help - справка

💡 ПРИМЕЧАНИЕ:
Скрипт работает с любой версией python-telegram-bot
Никаких дополнительных настроек не требуется!
