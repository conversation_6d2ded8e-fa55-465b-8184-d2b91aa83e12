# RelefXisoblator - Telegram Bot для измерения разницы высот

Telegram бот для автоматического измерения разницы высот между координатами с использованием Google Elevation API.

## Возможности

- Обработка Excel файлов с координатами, азимутом и дистанцией
- Автоматический расчет разницы высот между начальной и конечной точками
- Управление API ключами Google через Telegram интерфейс
- Автоматическая ротация API ключей при достижении лимитов
- Административные команды для управления API ключами

## Команды бота

### Основные команды
- `/start` - Начать работу с ботом
- `/help` - Получить справку по использованию бота

### Команды администратора
- `/addkey <ключ> <лимит>` - Добавить новый API ключ с указанным дневным лимитом
- `/listkeys` - Показать список всех API ключей и их использование
- `/removekey <ключ>` - Удалить API ключ
- `/resetusage` - Сбросить счетчики использования для всех ключей

## Формат входных данных

Бот принимает Excel файлы со следующей структурой:

```
coords          azimuth  distance
40.53648 70.94076;120;1000
40.53648 70.94076;120;1000
40.53648 70.94076;120;1000
```

где:
- `coords` - широта и долгота, разделенные пробелом
- `azimuth` - азимут в градусах
- `distance` - дистанция в метрах

## Установка и запуск

1. Клонировать репозиторий:
```
git clone https://github.com/ASayidov/RelefXisoblator.git
```

2. Установить зависимости:
```
pip install -r requirements.txt
```

3. Запустить бота:
```
python main.py
```

## Управление API ключами

Бот автоматически управляет API ключами Google Elevation API:
- Отслеживает дневные лимиты использования
- Автоматически переключается на другой ключ при достижении лимита
- Позволяет добавлять новые ключи через Telegram интерфейс

## Лицензия

MIT
