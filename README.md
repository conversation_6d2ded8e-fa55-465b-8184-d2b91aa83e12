# RelefXisoblator - Telegram Bot для измерения разницы высот

Telegram бот для автоматического измерения разницы высот между координатами с использованием Google Elevation API.

## Возможности

- Обработка Excel файлов с координатами, азимутом и дистанцией
- Автоматический расчет разницы высот между начальной и конечной точками
- **Персональные API ключи** - каждый пользователь использует свой Google Elevation API ключ
- Управление API ключами через Telegram интерфейс
- Административные команды для управления системными API ключами
- Защита от превышения лимитов API

## Команды бота

### Основные команды

- `/start` - Начать работу с ботом
- `/help` - Получить справку по использованию бота

### Команды для пользователей

- `/setkey <ваш_api_ключ>` - Установить/изменить свой API ключ
- `/mykey` - Посмотреть информацию о своем API ключе
- `/removekey` - Удалить свой API ключ

### Команды администратора

- `/addkey <ключ> <лимит>` - Добавить системный API ключ с указанным дневным лимитом
- `/listkeys` - Показать список системных API ключей и их использование
- `/removesystemkey <ключ>` - Удалить системный API ключ
- `/resetusage` - Сбросить счетчики использования для системных ключей

## Формат входных данных

Бот принимает Excel файлы со следующей структурой:

```
coords          azimuth  distance
40.53648 70.94076;120;1000
40.53648 70.94076;120;1000
40.53648 70.94076;120;1000
```

где:

- `coords` - широта и долгота, разделенные пробелом
- `azimuth` - азимут в градусах
- `distance` - дистанция в метрах

## Установка и запуск

1. Клонировать репозиторий:

```
git clone https://github.com/ASayidov/RelefXisoblator.git
```

2. Установить зависимости:

```
pip install -r requirements.txt
```

3. Запустить бота:

```
python main.py
```

## Как получить Google Elevation API ключ

1. Перейдите на [Google Cloud Console](https://console.cloud.google.com/)
2. Создайте новый проект или выберите существующий
3. Включите Google Maps Elevation API
4. Создайте API ключ в разделе "Credentials"
5. Используйте команду `/setkey <ваш_api_ключ>` в боте

## Управление API ключами

### Персональные API ключи

- Каждый пользователь устанавливает свой собственный API ключ
- Полная изоляция - никто не может использовать чужой ключ
- Отслеживание личной статистики использования
- Защита от превышения лимитов

### Системные API ключи (для администраторов)

- Резервные ключи для совместимости
- Автоматическая ротация при достижении лимитов
- Централизованное управление через Telegram интерфейс

## Лицензия

MIT
