#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Автоматический запуск бота с определением версии python-telegram-bot
"""

import sys
import subprocess
import os

def install_requirements():
    """Устанавливает необходимые пакеты"""
    try:
        # Сначала устанавливаем базовые пакеты
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pandas", "requests", "openpyxl", "nest_asyncio"])
        
        # Пробуем установить новую версию python-telegram-bot
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "python-telegram-bot==21.0"])
            print("✅ Установлена новая версия python-telegram-bot (21.0)")
            return "new"
        except:
            # Если не получилось, устанавливаем старую версию
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "python-telegram-bot==13.15"])
                print("✅ Установлена старая версия python-telegram-bot (13.15)")
                return "old"
            except:
                print("❌ Не удалось установить python-telegram-bot")
                return None
    except Exception as e:
        print(f"❌ Ошибка установки пакетов: {e}")
        return None

def detect_telegram_bot_version():
    """Определяет версию python-telegram-bot"""
    try:
        import telegram
        version = telegram.__version__
        print(f"📦 Обнаружена версия python-telegram-bot: {version}")
        
        # Определяем тип версии
        major_version = int(version.split('.')[0])
        if major_version >= 20:
            return "new"
        else:
            return "old"
    except ImportError:
        print("📦 python-telegram-bot не установлен, устанавливаем...")
        return install_requirements()
    except Exception as e:
        print(f"❌ Ошибка определения версии: {e}")
        return None

def run_new_version():
    """Запускает бот с новой версией python-telegram-bot"""
    print("🚀 Запуск бота с новой версией...")
    
    # Импорты для новой версии
    import logging
    import pandas as pd
    import requests
    import time
    import math
    import asyncio
    import nest_asyncio
    from telegram import Update
    from telegram.ext import Application, CommandHandler, MessageHandler, filters, CallbackContext
    import os
    import pytz
    
    # nest_asyncio применяем
    nest_asyncio.apply()
    os.environ['TZ'] = 'UTC'
    
    from api_config import api_key_manager, get_api_key, user_api_key_manager, get_user_api_key, ADMIN_IDS
    
    # Настройка логирования
    logging.basicConfig(
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        level=logging.INFO
    )
    logger = logging.getLogger(__name__)
    
    # Все функции бота (копируем из main.py)
    def get_elevation(lat, lon, user_id=None, retries=3, delay=2):
        api_key = None
        if user_id:
            api_key = get_user_api_key(user_id)
        
        if not api_key:
            api_key = get_api_key()
            if not api_key:
                logger.error("Нет доступных API ключей для использования.")
                return None

        url = f"https://maps.googleapis.com/maps/api/elevation/json?locations={lat},{lon}&key={api_key}"
        
        for attempt in range(retries):
            try:
                response = requests.get(url, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                
                if data['status'] == 'OK' and data['results']:
                    elevation = data['results'][0]['elevation']
                    logger.info(f"Получена высота для координат ({lat}, {lon}): {elevation} м")
                    return elevation
                else:
                    logger.error(f"Ошибка API: {data.get('status', 'Unknown error')}")
                    return None
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Ошибка запроса (попытка {attempt + 1}/{retries}): {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
                else:
                    return None
            except Exception as e:
                logger.error(f"Неожиданная ошибка: {e}")
                return None

    def calculate_new_coordinates(lat, lon, azimuth, distance):
        R = 6378137
        lat_rad = math.radians(lat)
        lon_rad = math.radians(lon)
        azimuth_rad = math.radians(azimuth)
        
        new_lat_rad = math.asin(math.sin(lat_rad) * math.cos(distance / R) +
                               math.cos(lat_rad) * math.sin(distance / R) * math.cos(azimuth_rad))
        
        new_lon_rad = lon_rad + math.atan2(math.sin(azimuth_rad) * math.sin(distance / R) * math.cos(lat_rad),
                                          math.cos(distance / R) - math.sin(lat_rad) * math.sin(new_lat_rad))
        
        new_lat = math.degrees(new_lat_rad)
        new_lon = math.degrees(new_lon_rad)
        
        return new_lat, new_lon

    async def start(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id
        
        if user_api_key_manager.has_user_api_key(user_id):
            await update.message.reply_text(
                '🎉 Добро пожаловать! У вас уже настроен API ключ.\n\n'
                '📁 Отправьте мне Excel файл с данными в формате:\n'
                'широта долгота; азимут; дистанция\n\n'
                '⚙️ Команды: /mykey, /setkey, /help'
            )
        else:
            await update.message.reply_text(
                '👋 Добро пожаловать в бот для расчета рельефа!\n\n'
                '🔑 Для работы установите свой Google Elevation API ключ:\n'
                '/setkey <ваш_api_ключ>\n\n'
                '📋 Как получить ключ: /help'
            )

    async def set_user_api_key(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id
        
        if not context.args or len(context.args) < 1:
            await update.message.reply_text(
                '❌ Укажите API ключ: /setkey <ваш_ключ>'
            )
            return

        api_key = context.args[0]

        if user_api_key_manager.set_user_api_key(user_id, api_key):
            await update.message.reply_text('✅ API ключ установлен!')
        else:
            await update.message.reply_text('❌ Ошибка установки ключа')

    async def show_user_api_key(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id
        
        if not user_api_key_manager.has_user_api_key(user_id):
            await update.message.reply_text('❌ API ключ не установлен. Используйте /setkey')
            return

        api_key = user_api_key_manager.get_user_api_key(user_id)
        usage_today = user_api_key_manager.get_user_usage(user_id)
        masked_key = api_key[:4] + "..." + api_key[-4:] if len(api_key) > 8 else "***"
        
        await update.message.reply_text(
            f'🔑 Ваш API ключ: {masked_key}\n'
            f'📊 Использовано сегодня: {usage_today} запросов'
        )

    async def remove_user_api_key(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id
        
        if user_api_key_manager.remove_user_api_key(user_id):
            await update.message.reply_text('✅ API ключ удален')
        else:
            await update.message.reply_text('❌ API ключ не найден')

    async def help_command(update: Update, context: CallbackContext) -> None:
        help_text = (
            '📖 Справка по боту\n\n'
            '📁 Отправьте Excel файл с данными:\n'
            'широта долгота; азимут; дистанция\n\n'
            '🔑 Команды:\n'
            '/setkey <ключ> - установить API ключ\n'
            '/mykey - информация о ключе\n'
            '/removekey - удалить ключ\n\n'
            '📋 Получение API ключа:\n'
            '1. console.cloud.google.com\n'
            '2. Создайте проект\n'
            '3. Включите Elevation API\n'
            '4. Создайте API ключ'
        )
        await update.message.reply_text(help_text)

    async def handle_document(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id
        
        if not user_api_key_manager.has_user_api_key(user_id):
            await update.message.reply_text(
                '❌ Установите API ключ: /setkey <ваш_ключ>'
            )
            return
        
        try:
            file = await context.bot.get_file(update.message.document.file_id)
            file_path = file.file_path

            df = pd.read_excel(file_path)
            
            if df.shape[1] < 3:
                await update.message.reply_text('❌ Файл должен содержать 3 столбца')
                return

            results = []
            
            for index, row in df.iterrows():
                try:
                    coords_str = str(row.iloc[0])
                    coords_parts = coords_str.split()
                    
                    if len(coords_parts) != 2:
                        continue
                    
                    lat = float(coords_parts[0])
                    lon = float(coords_parts[1])
                    azimuth = float(row.iloc[1])
                    distance = float(row.iloc[2])
                    
                    end_lat, end_lon = calculate_new_coordinates(lat, lon, azimuth, distance)
                    
                    start_elevation = get_elevation(lat, lon, user_id)
                    end_elevation = get_elevation(end_lat, end_lon, user_id)
                    
                    if start_elevation is not None and end_elevation is not None:
                        elevation_diff = end_elevation - start_elevation
                        
                        results.append({
                            'Строка': index + 1,
                            'Начальная широта': lat,
                            'Начальная долгота': lon,
                            'Конечная широта': round(end_lat, 6),
                            'Конечная долгота': round(end_lon, 6),
                            'Начальная высота (м)': round(start_elevation, 2),
                            'Конечная высота (м)': round(end_elevation, 2),
                            'Разница высот (м)': round(elevation_diff, 2)
                        })
                    
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"Ошибка строки {index + 1}: {e}")

            results_df = pd.DataFrame(results)
            output_filename = 'elevation_results.xlsx'
            results_df.to_excel(output_filename, index=False)
            
            with open(output_filename, 'rb') as file:
                await context.bot.send_document(
                    chat_id=update.effective_chat.id,
                    document=file,
                    filename=output_filename,
                    caption=f'✅ Обработано строк: {len(results)}'
                )
            
            if os.path.exists(output_filename):
                os.remove(output_filename)
                
        except Exception as e:
            logger.error(f"Ошибка обработки: {e}")
            await update.message.reply_text(f'❌ Ошибка: {str(e)}')

    async def main():
        application = Application.builder().token("7691215008:AAGgkRjMnkTwevgdaPKXWXrSVFHB_vNB844").job_queue(None).build()

        application.add_handler(CommandHandler("start", start))
        application.add_handler(CommandHandler("help", help_command))
        application.add_handler(CommandHandler("setkey", set_user_api_key))
        application.add_handler(CommandHandler("mykey", show_user_api_key))
        application.add_handler(CommandHandler("removekey", remove_user_api_key))
        application.add_handler(MessageHandler(filters.Document.ALL, handle_document))

        if not api_key_manager.get_active_keys():
            logger.warning("Системные API ключи не настроены")

        await application.run_polling()

    # Запуск
    asyncio.run(main())

def run_old_version():
    """Запускает бот со старой версией python-telegram-bot"""
    print("🚀 Запуск бота со старой версией...")
    
    # Импорты для старой версии
    import logging
    import pandas as pd
    import requests
    import time
    import math
    import nest_asyncio
    from telegram import Update
    from telegram.ext import Updater, CommandHandler, MessageHandler, Filters, CallbackContext
    import os
    
    nest_asyncio.apply()
    os.environ['TZ'] = 'UTC'
    
    from api_config import api_key_manager, get_api_key, user_api_key_manager, get_user_api_key, ADMIN_IDS
    
    logging.basicConfig(
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        level=logging.INFO
    )
    logger = logging.getLogger(__name__)
    
    # Все функции для старой версии (без async/await)
    def get_elevation(lat, lon, user_id=None, retries=3, delay=2):
        api_key = None
        if user_id:
            api_key = get_user_api_key(user_id)
        
        if not api_key:
            api_key = get_api_key()
            if not api_key:
                return None

        url = f"https://maps.googleapis.com/maps/api/elevation/json?locations={lat},{lon}&key={api_key}"
        
        for attempt in range(retries):
            try:
                response = requests.get(url, timeout=10)
                data = response.json()
                
                if data['status'] == 'OK' and data['results']:
                    return data['results'][0]['elevation']
                else:
                    return None
                    
            except Exception as e:
                if attempt < retries - 1:
                    time.sleep(delay)
                else:
                    return None

    def calculate_new_coordinates(lat, lon, azimuth, distance):
        R = 6378137
        lat_rad = math.radians(lat)
        lon_rad = math.radians(lon)
        azimuth_rad = math.radians(azimuth)
        
        new_lat_rad = math.asin(math.sin(lat_rad) * math.cos(distance / R) +
                               math.cos(lat_rad) * math.sin(distance / R) * math.cos(azimuth_rad))
        
        new_lon_rad = lon_rad + math.atan2(math.sin(azimuth_rad) * math.sin(distance / R) * math.cos(lat_rad),
                                          math.cos(distance / R) - math.sin(lat_rad) * math.sin(new_lat_rad))
        
        return math.degrees(new_lat_rad), math.degrees(new_lon_rad)

    def start(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id
        
        if user_api_key_manager.has_user_api_key(user_id):
            update.message.reply_text('🎉 API ключ настроен! Отправьте Excel файл.')
        else:
            update.message.reply_text('👋 Установите API ключ: /setkey <ваш_ключ>')

    def set_user_api_key(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id
        
        if not context.args:
            update.message.reply_text('❌ Укажите ключ: /setkey <ваш_ключ>')
            return

        if user_api_key_manager.set_user_api_key(user_id, context.args[0]):
            update.message.reply_text('✅ API ключ установлен!')
        else:
            update.message.reply_text('❌ Ошибка установки ключа')

    def show_user_api_key(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id
        
        if not user_api_key_manager.has_user_api_key(user_id):
            update.message.reply_text('❌ API ключ не установлен')
            return

        api_key = user_api_key_manager.get_user_api_key(user_id)
        usage = user_api_key_manager.get_user_usage(user_id)
        masked = api_key[:4] + "..." + api_key[-4:] if len(api_key) > 8 else "***"
        
        update.message.reply_text(f'🔑 Ключ: {masked}\n📊 Использовано: {usage}')

    def remove_user_api_key(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id
        
        if user_api_key_manager.remove_user_api_key(user_id):
            update.message.reply_text('✅ API ключ удален')
        else:
            update.message.reply_text('❌ Ключ не найден')

    def help_command(update: Update, context: CallbackContext) -> None:
        update.message.reply_text(
            '📖 Справка\n\n'
            '📁 Отправьте Excel: широта долгота; азимут; дистанция\n'
            '🔑 /setkey <ключ> - установить ключ\n'
            '📊 /mykey - информация о ключе'
        )

    def handle_document(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id
        
        if not user_api_key_manager.has_user_api_key(user_id):
            update.message.reply_text('❌ Установите API ключ: /setkey')
            return
        
        try:
            file = context.bot.get_file(update.message.document.file_id)
            df = pd.read_excel(file.file_path)
            
            results = []
            
            for index, row in df.iterrows():
                try:
                    coords = str(row.iloc[0]).split()
                    if len(coords) != 2:
                        continue
                    
                    lat, lon = float(coords[0]), float(coords[1])
                    azimuth, distance = float(row.iloc[1]), float(row.iloc[2])
                    
                    end_lat, end_lon = calculate_new_coordinates(lat, lon, azimuth, distance)
                    start_elev = get_elevation(lat, lon, user_id)
                    end_elev = get_elevation(end_lat, end_lon, user_id)
                    
                    if start_elev is not None and end_elev is not None:
                        results.append({
                            'Строка': index + 1,
                            'Начальная широта': lat,
                            'Начальная долгота': lon,
                            'Конечная широта': round(end_lat, 6),
                            'Конечная долгота': round(end_lon, 6),
                            'Начальная высота (м)': round(start_elev, 2),
                            'Конечная высота (м)': round(end_elev, 2),
                            'Разница высот (м)': round(end_elev - start_elev, 2)
                        })
                    
                    time.sleep(0.1)
                except:
                    continue

            results_df = pd.DataFrame(results)
            output_file = 'elevation_results.xlsx'
            results_df.to_excel(output_file, index=False)
            
            with open(output_file, 'rb') as f:
                context.bot.send_document(
                    chat_id=update.effective_chat.id,
                    document=f,
                    filename=output_file,
                    caption=f'✅ Обработано: {len(results)} строк'
                )
            
            if os.path.exists(output_file):
                os.remove(output_file)
                
        except Exception as e:
            update.message.reply_text(f'❌ Ошибка: {str(e)}')

    # Создаем updater и добавляем обработчики
    updater = Updater("7691215008:AAGgkRjMnkTwevgdaPKXWXrSVFHB_vNB844", use_context=True)
    dp = updater.dispatcher

    dp.add_handler(CommandHandler("start", start))
    dp.add_handler(CommandHandler("help", help_command))
    dp.add_handler(CommandHandler("setkey", set_user_api_key))
    dp.add_handler(CommandHandler("mykey", show_user_api_key))
    dp.add_handler(CommandHandler("removekey", remove_user_api_key))
    dp.add_handler(MessageHandler(Filters.document, handle_document))

    print("✅ Бот запущен!")
    updater.start_polling()
    updater.idle()

if __name__ == "__main__":
    print("🤖 Автоматический запуск Telegram бота...")
    
    # Определяем версию
    version_type = detect_telegram_bot_version()
    
    if version_type == "new":
        run_new_version()
    elif version_type == "old":
        run_old_version()
    else:
        print("❌ Не удалось определить или установить python-telegram-bot")
        print("💡 Попробуйте установить вручную:")
        print("   pip install python-telegram-bot")
