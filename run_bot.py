#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Автоматический запуск бота с определением версии python-telegram-bot
"""

import sys
import subprocess
import os

def install_requirements():
    """Устанавливает необходимые пакеты"""
    try:
        # Сначала устанавливаем базовые пакеты
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pandas", "requests", "openpyxl", "nest_asyncio"])

        # Пробуем установить новую версию python-telegram-bot
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "python-telegram-bot==21.0"])
            print("✅ Установлена новая версия python-telegram-bot (21.0)")
            return "new"
        except:
            # Если не получилось, устанавливаем старую версию
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "python-telegram-bot==13.15"])
                print("✅ Установлена старая версия python-telegram-bot (13.15)")
                return "old"
            except:
                print("❌ Не удалось установить python-telegram-bot")
                return None
    except Exception as e:
        print(f"❌ Ошибка установки пакетов: {e}")
        return None

def detect_telegram_bot_version():
    """Определяет версию python-telegram-bot"""
    try:
        import telegram
        version = telegram.__version__
        print(f"📦 Обнаружена версия python-telegram-bot: {version}")

        # Определяем тип версии
        major_version = int(version.split('.')[0])
        if major_version >= 20:
            return "new"
        else:
            return "old"
    except ImportError:
        print("📦 python-telegram-bot не установлен, устанавливаем...")
        return install_requirements()
    except Exception as e:
        print(f"❌ Ошибка определения версии: {e}")
        return None

def run_new_version():
    """Запускает бот с новой версией python-telegram-bot"""
    print("🚀 Запуск бота с новой версией...")

    # Импорты для новой версии
    import logging
    import pandas as pd
    import requests
    import time
    import math
    import asyncio
    import nest_asyncio
    from telegram import Update
    from telegram.ext import Application, CommandHandler, MessageHandler, filters, CallbackContext
    import os
    import pytz

    # nest_asyncio применяем
    nest_asyncio.apply()
    os.environ['TZ'] = 'UTC'

    from api_config import api_key_manager, get_api_key, user_api_key_manager, get_user_api_key, ADMIN_IDS

    # Настройка логирования
    logging.basicConfig(
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        level=logging.INFO
    )
    logger = logging.getLogger(__name__)

    # Все функции бота (копируем из main.py)
    def get_elevation(lat, lon, user_id=None, retries=3, delay=2):
        api_key = None
        if user_id:
            api_key = get_user_api_key(user_id)

        if not api_key:
            api_key = get_api_key()
            if not api_key:
                logger.error("Нет доступных API ключей для использования.")
                return None

        url = f"https://maps.googleapis.com/maps/api/elevation/json?locations={lat},{lon}&key={api_key}"

        for attempt in range(retries):
            try:
                response = requests.get(url, timeout=10)
                response.raise_for_status()

                data = response.json()

                if data['status'] == 'OK' and data['results']:
                    elevation = data['results'][0]['elevation']
                    logger.info(f"Получена высота для координат ({lat}, {lon}): {elevation} м")
                    return elevation
                else:
                    logger.error(f"Ошибка API: {data.get('status', 'Unknown error')}")
                    return None

            except requests.exceptions.RequestException as e:
                logger.error(f"Ошибка запроса (попытка {attempt + 1}/{retries}): {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
                else:
                    return None
            except Exception as e:
                logger.error(f"Неожиданная ошибка: {e}")
                return None

    def calculate_new_coordinates(lat, lon, azimuth, distance):
        R = 6378137
        lat_rad = math.radians(lat)
        lon_rad = math.radians(lon)
        azimuth_rad = math.radians(azimuth)

        new_lat_rad = math.asin(math.sin(lat_rad) * math.cos(distance / R) +
                               math.cos(lat_rad) * math.sin(distance / R) * math.cos(azimuth_rad))

        new_lon_rad = lon_rad + math.atan2(math.sin(azimuth_rad) * math.sin(distance / R) * math.cos(lat_rad),
                                          math.cos(distance / R) - math.sin(lat_rad) * math.sin(new_lat_rad))

        new_lat = math.degrees(new_lat_rad)
        new_lon = math.degrees(new_lon_rad)

        return new_lat, new_lon

    async def start(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id

        if user_api_key_manager.has_user_api_key(user_id):
            await update.message.reply_text(
                '🎉 Добро пожаловать! У вас уже настроен API ключ.\n\n'
                '📁 Отправьте мне Excel файл с данными в формате:\n'
                'широта долгота; азимут; дистанция\n\n'
                '⚙️ Команды: /mykey, /setkey, /help'
            )
        else:
            await update.message.reply_text(
                '👋 Добро пожаловать в бот для расчета рельефа!\n\n'
                '🔑 Для работы установите свой Google Elevation API ключ:\n'
                '/setkey <ваш_api_ключ>\n\n'
                '📋 Как получить ключ: /help'
            )

    async def set_user_api_key(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id

        if not context.args or len(context.args) < 1:
            await update.message.reply_text(
                '❌ Укажите API ключ: /setkey <ваш_ключ>'
            )
            return

        api_key = context.args[0]

        if user_api_key_manager.set_user_api_key(user_id, api_key):
            await update.message.reply_text('✅ API ключ установлен!')
        else:
            await update.message.reply_text('❌ Ошибка установки ключа')

    async def show_user_api_key(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id

        if not user_api_key_manager.has_user_api_key(user_id):
            await update.message.reply_text('❌ API ключ не установлен. Используйте /setkey')
            return

        api_key = user_api_key_manager.get_user_api_key(user_id)
        usage_today = user_api_key_manager.get_user_usage(user_id)
        masked_key = api_key[:4] + "..." + api_key[-4:] if len(api_key) > 8 else "***"

        await update.message.reply_text(
            f'🔑 Ваш API ключ: {masked_key}\n'
            f'📊 Использовано сегодня: {usage_today} запросов'
        )

    async def remove_user_api_key(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id

        if user_api_key_manager.remove_user_api_key(user_id):
            await update.message.reply_text('✅ API ключ удален')
        else:
            await update.message.reply_text('❌ API ключ не найден')

    async def help_command(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id

        # Базовая справка
        base_help = (
            '📖 Справка по использованию бота\n\n'
            '📁 Для работы отправьте Excel файл с данными в формате:\n'
            'coords; azimuth; distance\n\n'
            '📝 Где:\n'
            '• coords: широта и долгота через пробел (например, "40.53648 70.94076")\n'
            '• azimuth: азимут в градусах (например, "120")\n'
            '• distance: дистанция в метрах (например, "1000")\n\n'
            '📋 Пример содержимого Excel файла:\n'
            '40.53648 70.94076;120;1000\n'
            '40.53648 70.94076;120;1000\n'
            '40.53648 70.94076;120;1000\n\n'
            '📊 После отправки файла бот вычислит разницу высот и отправит результат.\n\n'
            '🔑 ПОЛНОЕ РУКОВОДСТВО ПО ПОЛУЧЕНИЮ GOOGLE ELEVATION API:\n\n'
            '📋 Шаг 1: Создание аккаунта Google\n'
            '• Перейдите на console.cloud.google.com\n'
            '• Войдите в аккаунт Google или создайте новый\n'
            '• Примите условия использования Google Cloud Platform\n\n'
            '📋 Шаг 2: Создание нового проекта\n'
            '• В верхней части нажмите на выпадающий список проектов\n'
            '• Нажмите "NEW PROJECT" (Новый проект)\n'
            '• Введите название проекта (например: "My Elevation API Project")\n'
            '• Нажмите "CREATE" (Создать)\n\n'
            '📋 Шаг 3: Включение Elevation API\n'
            '• В левом меню найдите "APIs & Services" → "Library"\n'
            '• В поисковой строке введите "Elevation API"\n'
            '• Найдите "Elevation API" и нажмите "ENABLE" (Включить)\n\n'
        )
        await update.message.reply_text(base_help)

        # Продолжение руководства
        billing_help = (
            '📋 Шаг 4: Настройка биллинга (ОБЯЗАТЕЛЬНО!)\n'
            '⚠️ Даже для бесплатного использования нужна карта!\n\n'
            '4.1 Переход к настройкам биллинга:\n'
            '• В левом меню найдите "Billing" (Биллинг)\n'
            '• Нажмите "LINK A BILLING ACCOUNT"\n\n'
            '4.2 Создание биллинг аккаунта:\n'
            '• Нажмите "CREATE BILLING ACCOUNT"\n'
            '• Выберите страну/регион\n'
            '• Введите название биллинг аккаунта\n\n'
            '4.3 Добавление платежной информации:\n'
            '• Выберите "Individual" (Частное лицо)\n'
            '• Заполните контактную информацию:\n'
            '  - Имя и фамилия\n'
            '  - Адрес, город, почтовый индекс\n'
            '  - Страна\n'
            '• Добавьте банковскую карту:\n'
            '  - Нажмите "ADD PAYMENT METHOD"\n'
            '  - Выберите "Credit or debit card"\n'
            '  - Номер карты (16 цифр)\n'
            '  - Месяц/год истечения (MM/YY)\n'
            '  - CVC код (3 цифры на обороте)\n'
            '  - Имя держателя карты (как на карте)\n'
            '• Нажмите "START MY FREE TRIAL"\n\n'
            '💡 Google может списать $1 для верификации\n'
            '(сумма вернется через несколько дней)\n'
        )
        await update.message.reply_text(billing_help)

        # Финальная часть
        final_help = (
            '📋 Шаг 5: Создание API ключа\n'
            '• Вернитесь в "APIs & Services" → "Credentials"\n'
            '• Нажмите "+ CREATE CREDENTIALS"\n'
            '• Выберите "API key"\n'
            '• Скопируйте созданный ключ и сохраните в безопасном месте\n'
            '• Нажмите "CLOSE"\n\n'
            '📋 Шаг 6: Настройка ограничений (рекомендуется)\n'
            '• Нажмите на созданный API ключ для редактирования\n'
            '• В разделе "API restrictions" выберите:\n'
            '  "Restrict key" → отметьте "Maps Elevation API"\n'
            '• В разделе "Application restrictions" можно:\n'
            '  - Оставить "None" для простоты\n'
            '  - Или настроить ограничения по IP/домену\n'
            '• Нажмите "SAVE"\n\n'
            '🎯 ГОТОВО! Теперь используйте ваш ключ:\n'
            '/setkey <ваш_api_ключ>\n\n'
            '💡 Пример: /setkey AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx\n\n'
            '📊 Бесплатный лимит: 40,000 запросов в месяц\n'
            '💰 После превышения: ~$5 за 1000 запросов\n'
        )

        if user_api_key_manager.has_user_api_key(user_id):
            user_commands = (
                '⚙️ Ваши команды:\n'
                '/mykey - информация о вашем API ключе\n'
                '/setkey <новый_ключ> - изменить API ключ\n'
                '/removekey - удалить ваш API ключ\n'
            )
            await update.message.reply_text(final_help + '\n' + user_commands)
        else:
            await update.message.reply_text(final_help)

    async def handle_document(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id

        if not user_api_key_manager.has_user_api_key(user_id):
            await update.message.reply_text(
                '❌ Установите API ключ: /setkey <ваш_ключ>'
            )
            return

        try:
            file = await context.bot.get_file(update.message.document.file_id)
            file_path = file.file_path

            df = pd.read_excel(file_path)

            if df.shape[1] < 3:
                await update.message.reply_text('❌ Файл должен содержать 3 столбца')
                return

            results = []

            for index, row in df.iterrows():
                try:
                    coords_str = str(row.iloc[0])
                    coords_parts = coords_str.split()

                    if len(coords_parts) != 2:
                        continue

                    lat = float(coords_parts[0])
                    lon = float(coords_parts[1])
                    azimuth = float(row.iloc[1])
                    distance = float(row.iloc[2])

                    end_lat, end_lon = calculate_new_coordinates(lat, lon, azimuth, distance)

                    start_elevation = get_elevation(lat, lon, user_id)
                    end_elevation = get_elevation(end_lat, end_lon, user_id)

                    if start_elevation is not None and end_elevation is not None:
                        elevation_diff = end_elevation - start_elevation

                        results.append({
                            'Строка': index + 1,
                            'Начальная широта': lat,
                            'Начальная долгота': lon,
                            'Конечная широта': round(end_lat, 6),
                            'Конечная долгота': round(end_lon, 6),
                            'Начальная высота (м)': round(start_elevation, 2),
                            'Конечная высота (м)': round(end_elevation, 2),
                            'Разница высот (м)': round(elevation_diff, 2)
                        })

                    time.sleep(0.1)

                except Exception as e:
                    logger.error(f"Ошибка строки {index + 1}: {e}")

            results_df = pd.DataFrame(results)
            output_filename = 'elevation_results.xlsx'
            results_df.to_excel(output_filename, index=False)

            with open(output_filename, 'rb') as file:
                await context.bot.send_document(
                    chat_id=update.effective_chat.id,
                    document=file,
                    filename=output_filename,
                    caption=f'✅ Обработано строк: {len(results)}'
                )

            if os.path.exists(output_filename):
                os.remove(output_filename)

        except Exception as e:
            logger.error(f"Ошибка обработки: {e}")
            await update.message.reply_text(f'❌ Ошибка: {str(e)}')

    async def main():
        application = Application.builder().token("7691215008:AAGgkRjMnkTwevgdaPKXWXrSVFHB_vNB844").job_queue(None).build()

        application.add_handler(CommandHandler("start", start))
        application.add_handler(CommandHandler("help", help_command))
        application.add_handler(CommandHandler("setkey", set_user_api_key))
        application.add_handler(CommandHandler("mykey", show_user_api_key))
        application.add_handler(CommandHandler("removekey", remove_user_api_key))
        application.add_handler(MessageHandler(filters.Document.ALL, handle_document))

        if not api_key_manager.get_active_keys():
            logger.warning("Системные API ключи не настроены")

        await application.run_polling()

    # Запуск
    asyncio.run(main())

def run_old_version():
    """Запускает бот со старой версией python-telegram-bot"""
    print("🚀 Запуск бота со старой версией...")

    # Импорты для старой версии
    import logging
    import pandas as pd
    import requests
    import time
    import math
    import nest_asyncio
    from telegram import Update
    from telegram.ext import Updater, CommandHandler, MessageHandler, Filters, CallbackContext
    import os

    nest_asyncio.apply()
    os.environ['TZ'] = 'UTC'

    from api_config import api_key_manager, get_api_key, user_api_key_manager, get_user_api_key, ADMIN_IDS

    logging.basicConfig(
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        level=logging.INFO
    )
    logger = logging.getLogger(__name__)

    # Все функции для старой версии (без async/await)
    def get_elevation(lat, lon, user_id=None, retries=3, delay=2):
        api_key = None
        if user_id:
            api_key = get_user_api_key(user_id)

        if not api_key:
            api_key = get_api_key()
            if not api_key:
                return None

        url = f"https://maps.googleapis.com/maps/api/elevation/json?locations={lat},{lon}&key={api_key}"

        for attempt in range(retries):
            try:
                response = requests.get(url, timeout=10)
                data = response.json()

                if data['status'] == 'OK' and data['results']:
                    return data['results'][0]['elevation']
                else:
                    return None

            except Exception as e:
                if attempt < retries - 1:
                    time.sleep(delay)
                else:
                    return None

    def calculate_new_coordinates(lat, lon, azimuth, distance):
        R = 6378137
        lat_rad = math.radians(lat)
        lon_rad = math.radians(lon)
        azimuth_rad = math.radians(azimuth)

        new_lat_rad = math.asin(math.sin(lat_rad) * math.cos(distance / R) +
                               math.cos(lat_rad) * math.sin(distance / R) * math.cos(azimuth_rad))

        new_lon_rad = lon_rad + math.atan2(math.sin(azimuth_rad) * math.sin(distance / R) * math.cos(lat_rad),
                                          math.cos(distance / R) - math.sin(lat_rad) * math.sin(new_lat_rad))

        return math.degrees(new_lat_rad), math.degrees(new_lon_rad)

    def start(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id

        if user_api_key_manager.has_user_api_key(user_id):
            update.message.reply_text('🎉 API ключ настроен! Отправьте Excel файл.')
        else:
            update.message.reply_text('👋 Установите API ключ: /setkey <ваш_ключ>')

    def set_user_api_key(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id

        if not context.args:
            update.message.reply_text('❌ Укажите ключ: /setkey <ваш_ключ>')
            return

        if user_api_key_manager.set_user_api_key(user_id, context.args[0]):
            update.message.reply_text('✅ API ключ установлен!')
        else:
            update.message.reply_text('❌ Ошибка установки ключа')

    def show_user_api_key(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id

        if not user_api_key_manager.has_user_api_key(user_id):
            update.message.reply_text('❌ API ключ не установлен')
            return

        api_key = user_api_key_manager.get_user_api_key(user_id)
        usage = user_api_key_manager.get_user_usage(user_id)
        masked = api_key[:4] + "..." + api_key[-4:] if len(api_key) > 8 else "***"

        update.message.reply_text(f'🔑 Ключ: {masked}\n📊 Использовано: {usage}')

    def remove_user_api_key(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id

        if user_api_key_manager.remove_user_api_key(user_id):
            update.message.reply_text('✅ API ключ удален')
        else:
            update.message.reply_text('❌ Ключ не найден')

    def help_command(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id

        # Базовая справка
        base_help = (
            '📖 Справка по использованию бота\n\n'
            '📁 Для работы отправьте Excel файл с данными в формате:\n'
            'coords; azimuth; distance\n\n'
            '📝 Где:\n'
            '• coords: широта и долгота через пробел (например, "40.53648 70.94076")\n'
            '• azimuth: азимут в градусах (например, "120")\n'
            '• distance: дистанция в метрах (например, "1000")\n\n'
            '📋 Пример содержимого Excel файла:\n'
            '40.53648 70.94076;120;1000\n'
            '40.53648 70.94076;120;1000\n'
            '40.53648 70.94076;120;1000\n\n'
            '📊 После отправки файла бот вычислит разницу высот и отправит результат.\n\n'
            '🔑 ПОЛНОЕ РУКОВОДСТВО ПО ПОЛУЧЕНИЮ GOOGLE ELEVATION API:\n\n'
            '📋 Шаг 1: Создание аккаунта Google\n'
            '• Перейдите на console.cloud.google.com\n'
            '• Войдите в аккаунт Google или создайте новый\n'
            '• Примите условия использования Google Cloud Platform\n\n'
            '📋 Шаг 2: Создание нового проекта\n'
            '• В верхней части нажмите на выпадающий список проектов\n'
            '• Нажмите "NEW PROJECT" (Новый проект)\n'
            '• Введите название проекта (например: "My Elevation API Project")\n'
            '• Нажмите "CREATE" (Создать)\n\n'
            '📋 Шаг 3: Включение Elevation API\n'
            '• В левом меню найдите "APIs & Services" → "Library"\n'
            '• В поисковой строке введите "Elevation API"\n'
            '• Найдите "Elevation API" и нажмите "ENABLE" (Включить)\n\n'
        )
        update.message.reply_text(base_help)

        # Продолжение руководства
        billing_help = (
            '📋 Шаг 4: Настройка биллинга (ОБЯЗАТЕЛЬНО!)\n'
            '⚠️ Даже для бесплатного использования нужна карта!\n\n'
            '4.1 Переход к настройкам биллинга:\n'
            '• В левом меню найдите "Billing" (Биллинг)\n'
            '• Нажмите "LINK A BILLING ACCOUNT"\n\n'
            '4.2 Создание биллинг аккаунта:\n'
            '• Нажмите "CREATE BILLING ACCOUNT"\n'
            '• Выберите страну/регион\n'
            '• Введите название биллинг аккаунта\n\n'
            '4.3 Добавление платежной информации:\n'
            '• Выберите "Individual" (Частное лицо)\n'
            '• Заполните контактную информацию:\n'
            '  - Имя и фамилия\n'
            '  - Адрес, город, почтовый индекс\n'
            '  - Страна\n'
            '• Добавьте банковскую карту:\n'
            '  - Нажмите "ADD PAYMENT METHOD"\n'
            '  - Выберите "Credit or debit card"\n'
            '  - Номер карты (16 цифр)\n'
            '  - Месяц/год истечения (MM/YY)\n'
            '  - CVC код (3 цифры на обороте)\n'
            '  - Имя держателя карты (как на карте)\n'
            '• Нажмите "START MY FREE TRIAL"\n\n'
            '💡 Google может списать $1 для верификации\n'
            '(сумма вернется через несколько дней)\n'
        )
        update.message.reply_text(billing_help)

        # Финальная часть
        final_help = (
            '📋 Шаг 5: Создание API ключа\n'
            '• Вернитесь в "APIs & Services" → "Credentials"\n'
            '• Нажмите "+ CREATE CREDENTIALS"\n'
            '• Выберите "API key"\n'
            '• Скопируйте созданный ключ и сохраните в безопасном месте\n'
            '• Нажмите "CLOSE"\n\n'
            '📋 Шаг 6: Настройка ограничений (рекомендуется)\n'
            '• Нажмите на созданный API ключ для редактирования\n'
            '• В разделе "API restrictions" выберите:\n'
            '  "Restrict key" → отметьте "Maps Elevation API"\n'
            '• В разделе "Application restrictions" можно:\n'
            '  - Оставить "None" для простоты\n'
            '  - Или настроить ограничения по IP/домену\n'
            '• Нажмите "SAVE"\n\n'
            '🎯 ГОТОВО! Теперь используйте ваш ключ:\n'
            '/setkey <ваш_api_ключ>\n\n'
            '💡 Пример: /setkey AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx\n\n'
            '📊 Бесплатный лимит: 40,000 запросов в месяц\n'
            '💰 После превышения: ~$5 за 1000 запросов\n'
        )

        if user_api_key_manager.has_user_api_key(user_id):
            user_commands = (
                '⚙️ Ваши команды:\n'
                '/mykey - информация о вашем API ключе\n'
                '/setkey <новый_ключ> - изменить API ключ\n'
                '/removekey - удалить ваш API ключ\n'
            )
            update.message.reply_text(final_help + '\n' + user_commands)
        else:
            update.message.reply_text(final_help)

    def handle_document(update: Update, context: CallbackContext) -> None:
        user_id = update.effective_user.id

        if not user_api_key_manager.has_user_api_key(user_id):
            update.message.reply_text('❌ Установите API ключ: /setkey')
            return

        try:
            file = context.bot.get_file(update.message.document.file_id)
            df = pd.read_excel(file.file_path)

            results = []

            for index, row in df.iterrows():
                try:
                    coords = str(row.iloc[0]).split()
                    if len(coords) != 2:
                        continue

                    lat, lon = float(coords[0]), float(coords[1])
                    azimuth, distance = float(row.iloc[1]), float(row.iloc[2])

                    end_lat, end_lon = calculate_new_coordinates(lat, lon, azimuth, distance)
                    start_elev = get_elevation(lat, lon, user_id)
                    end_elev = get_elevation(end_lat, end_lon, user_id)

                    if start_elev is not None and end_elev is not None:
                        results.append({
                            'Строка': index + 1,
                            'Начальная широта': lat,
                            'Начальная долгота': lon,
                            'Конечная широта': round(end_lat, 6),
                            'Конечная долгота': round(end_lon, 6),
                            'Начальная высота (м)': round(start_elev, 2),
                            'Конечная высота (м)': round(end_elev, 2),
                            'Разница высот (м)': round(end_elev - start_elev, 2)
                        })

                    time.sleep(0.1)
                except:
                    continue

            results_df = pd.DataFrame(results)
            output_file = 'elevation_results.xlsx'
            results_df.to_excel(output_file, index=False)

            with open(output_file, 'rb') as f:
                context.bot.send_document(
                    chat_id=update.effective_chat.id,
                    document=f,
                    filename=output_file,
                    caption=f'✅ Обработано: {len(results)} строк'
                )

            if os.path.exists(output_file):
                os.remove(output_file)

        except Exception as e:
            update.message.reply_text(f'❌ Ошибка: {str(e)}')

    # Создаем updater и добавляем обработчики
    updater = Updater("7691215008:AAGgkRjMnkTwevgdaPKXWXrSVFHB_vNB844", use_context=True)
    dp = updater.dispatcher

    dp.add_handler(CommandHandler("start", start))
    dp.add_handler(CommandHandler("help", help_command))
    dp.add_handler(CommandHandler("setkey", set_user_api_key))
    dp.add_handler(CommandHandler("mykey", show_user_api_key))
    dp.add_handler(CommandHandler("removekey", remove_user_api_key))
    dp.add_handler(MessageHandler(Filters.document, handle_document))

    print("✅ Бот запущен!")
    updater.start_polling()
    updater.idle()

if __name__ == "__main__":
    print("🤖 Автоматический запуск Telegram бота...")

    # Определяем версию
    version_type = detect_telegram_bot_version()

    if version_type == "new":
        run_new_version()
    elif version_type == "old":
        run_old_version()
    else:
        print("❌ Не удалось определить или установить python-telegram-bot")
        print("💡 Попробуйте установить вручную:")
        print("   pip install python-telegram-bot")
